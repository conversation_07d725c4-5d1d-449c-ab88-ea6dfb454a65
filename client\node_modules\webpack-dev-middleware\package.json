{"name": "webpack-dev-middleware", "version": "5.3.4", "description": "A development middleware for webpack", "license": "MIT", "repository": "webpack/webpack-dev-middleware", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack/webpack-dev-middleware", "bugs": "https://github.com/webpack/webpack-dev-middleware/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "types/index.d.ts", "engines": {"node": ">= 12.13.0"}, "scripts": {"commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "clean": "del-cli dist types", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir types && prettier \"types/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist", "types"], "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.16.7", "@babel/core": "^7.16.7", "@babel/preset-env": "^7.16.7", "@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@types/connect": "^3.4.35", "@types/express": "^4.17.13", "@types/mime-types": "^2.1.1", "@types/node": "^12.20.43", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^28.1.0", "chokidar": "^3.5.1", "connect": "^3.7.0", "cross-env": "^7.0.3", "deepmerge": "^4.2.2", "del": "^6.0.0", "del-cli": "^4.0.0", "eslint": "^8.6.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "execa": "^5.1.1", "express": "^4.17.1", "file-loader": "^6.2.0", "husky": "^7.0.0", "jest": "^28.1.0", "lint-staged": "^12.1.7", "npm-run-all": "^4.1.5", "prettier": "^2.5.0", "standard-version": "^9.3.0", "strip-ansi": "^6.0.0", "supertest": "^6.1.3", "typescript": "4.5.5", "webpack": "^5.68.0"}, "keywords": ["webpack", "middleware", "development"]}