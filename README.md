# Mafia Game

A web-based Mafia game application with video chat integration and real-time gameplay.

## Features

- **Game Setup & Player Management**
  - Minimum 3 players required to start
  - Creator sets total players and mafia count
  - Mafia count always less than half total players
  - Join using unique game creation ID
  - Public/private game options
  - Random role assignment

- **Video Chat Integration**
  - Seamless video chat transition
  - Individual camera/microphone controls
  - Mute/unmute and camera on/off
  - Clean participant management UI

- **Game Flow & Phases**
  - Mafia Reveal Phase (5 seconds)
  - Discussion Phase (configurable, max 3 minutes)
  - Voting Phase with elimination
  - Elimination reveal with role display
  - Final Discussion (1 minute)

- **Game Mechanics**
  - Role-based visibility (mafia see each other, civilians don't)
  - Eliminated players can observe but not participate
  - Win conditions: All mafia eliminated OR mafia equals/outnumbers civilians
  - Real-time game state synchronization

## Tech Stack

- **Backend**: Node.js, Express, Socket.io
- **Frontend**: React, Socket.io-client
- **Video Chat**: WebRTC
- **Real-time Communication**: Socket.io
- **Styling**: CSS3 with modern UI design

## Installation

1. Clone the repository
```bash
git clone <repository-url>
cd mafia-game
```

2. Install dependencies
```bash
npm run install-all
```

3. Start development servers
```bash
npm run dev
```

This will start:
- Backend server on http://localhost:5000
- Frontend development server on http://localhost:3000

## Game Rules

### Setup
- Game creator sets the number of players and mafia members
- Mafia count must be less than half the total players
- Minimum 3 players required

### Phases
1. **Mafia Reveal** (5 seconds): Mafia members see each other
2. **Discussion**: Players discuss and share suspicions
3. **Voting**: Players vote to eliminate suspected mafia
4. **Elimination**: Reveal eliminated player's role
5. **Repeat** until win condition met
6. **Final Discussion**: All players return for final thoughts

### Win Conditions
- **Civilians Win**: All mafia members eliminated
- **Mafia Wins**: Mafia equals or outnumbers civilians

## Development

### Project Structure
```
mafia-game/
├── server/           # Backend Express server
│   ├── index.js      # Main server file
│   └── ...
├── client/           # React frontend
│   ├── src/
│   ├── public/
│   └── ...
├── package.json      # Root package.json
└── README.md
```

### Available Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run server` - Start only the backend server
- `npm run client` - Start only the frontend development server
- `npm run build` - Build the frontend for production
- `npm start` - Start the production server

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License
