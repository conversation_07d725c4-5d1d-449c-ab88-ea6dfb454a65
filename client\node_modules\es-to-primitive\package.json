{"name": "es-to-primitive", "version": "1.3.0", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ECMAScript “ToPrimitive” algorithm. Provides ES5 and ES2015 versions.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/es-to-primitive.git"}, "keywords": ["primitive", "abstract", "ecmascript", "es5", "es6", "es2015", "toPrimitive", "coerce", "type", "object", "string", "number", "boolean", "symbol", "null", "undefined"], "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/for-each": "^0.3.3", "@types/function.prototype.name": "^1.1.3", "@types/is-callable": "^1.1.2", "@types/is-date-object": "^1.0.4", "@types/is-symbol": "^1.0.2", "@types/object-inspect": "^1.13.0", "@types/object-is": "^1.1.0", "@types/tape": "^5.6.4", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "function.prototype.name": "^1.1.6", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "object-is": "^1.1.6", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "1.2.2"}, "publishConfig": {"ignore": [".github/workflows"]}}