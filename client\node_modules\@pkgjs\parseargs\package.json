{"name": "@pkgjs/parseargs", "version": "0.11.0", "description": "Polyfill of future proposal for `util.parseArgs()`", "engines": {"node": ">=14"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"coverage": "c8 --check-coverage tape 'test/*.js'", "test": "c8 tape 'test/*.js'", "posttest": "eslint .", "fix": "npm run posttest -- --fix"}, "repository": {"type": "git", "url": "**************:pkgjs/parseargs.git"}, "keywords": [], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/pkgjs/parseargs/issues"}, "homepage": "https://github.com/pkgjs/parseargs#readme", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "iansu/eslint-plugin-node-core", "tape": "^5.2.2"}}