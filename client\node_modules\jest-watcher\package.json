{"name": "jest-watcher", "description": "Delightful JavaScript Testing.", "version": "27.5.1", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "jest-util": "^27.5.1", "string-length": "^4.0.1"}, "repository": {"type": "git", "url": "https://github.com/facebook/jest", "directory": "packages/jest-watcher"}, "bugs": {"url": "https://github.com/facebook/jest/issues"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "homepage": "https://jestjs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}