{"name": "workbox-background-sync", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "Queues failed requests and uses the Background Sync API to replay them when the network is available", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "background", "sync", "workbox-plugin"], "workbox": {"browserNamespace": "workbox.backgroundSync", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"idb": "^7.0.1", "workbox-core": "6.6.0"}, "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}