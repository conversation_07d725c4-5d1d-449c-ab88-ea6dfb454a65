{"name": "object.entries", "version": "1.1.9", "author": "<PERSON>", "description": "ES2017 spec-compliant Object.entries shim.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "npx npm@\">= 10.2\" audit --production", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/Object.entries.git"}, "keywords": ["Object.entries", "Object.values", "Object.keys", "entries", "values", "ES7", "ES8", "ES2017", "shim", "object", "keys", "polyfill", "es-shim API"], "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-object-atoms": "^1.1.1"}, "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "array.prototype.map": "^1.0.8", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}