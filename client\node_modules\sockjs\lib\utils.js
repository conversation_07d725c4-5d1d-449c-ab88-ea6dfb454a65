// Generated by CoffeeScript 1.12.7
(function() {
  var array_intersection, crypto, escapable, lookup, unroll_lookup;

  crypto = require('crypto');

  exports.array_intersection = array_intersection = function(arr_a, arr_b) {
    var a, j, len, r;
    r = [];
    for (j = 0, len = arr_a.length; j < len; j++) {
      a = arr_a[j];
      if (arr_b.indexOf(a) !== -1) {
        r.push(a);
      }
    }
    return r;
  };

  exports.escape_selected = function(str, chars) {
    var c, i, j, l, len, map, parts, r, ref, v;
    map = {};
    chars = '%' + chars;
    for (j = 0, len = chars.length; j < len; j++) {
      c = chars[j];
      map[c] = escape(c);
    }
    r = new RegExp('([' + chars + '])');
    parts = str.split(r);
    for (i = l = 0, ref = parts.length; 0 <= ref ? l < ref : l > ref; i = 0 <= ref ? ++l : --l) {
      v = parts[i];
      if (v.length === 1 && v in map) {
        parts[i] = map[v];
      }
    }
    return parts.join('');
  };

  exports.buffer_concat = function(buf_a, buf_b) {
    var dst;
    dst = new Buffer(buf_a.length + buf_b.length);
    buf_a.copy(dst);
    buf_b.copy(dst, buf_a.length);
    return dst;
  };

  exports.md5_hex = function(data) {
    return crypto.createHash('md5').update(data).digest('hex');
  };

  exports.sha1_base64 = function(data) {
    return crypto.createHash('sha1').update(data).digest('base64');
  };

  exports.timeout_chain = function(arr) {
    var fun, ref, timeout, user_fun;
    arr = arr.slice(0);
    if (!arr.length) {
      return;
    }
    ref = arr.shift(), timeout = ref[0], user_fun = ref[1];
    fun = (function(_this) {
      return function() {
        user_fun();
        return exports.timeout_chain(arr);
      };
    })(this);
    return setTimeout(fun, timeout);
  };

  exports.objectExtend = function(dst, src) {
    var k;
    for (k in src) {
      if (src.hasOwnProperty(k)) {
        dst[k] = src[k];
      }
    }
    return dst;
  };

  exports.overshadowListeners = function(ee, event, handler) {
    var new_handler, old_listeners;
    old_listeners = ee.listeners(event).slice(0);
    ee.removeAllListeners(event);
    new_handler = function() {
      var j, len, listener;
      if (handler.apply(this, arguments) !== true) {
        for (j = 0, len = old_listeners.length; j < len; j++) {
          listener = old_listeners[j];
          listener.apply(this, arguments);
        }
        return false;
      }
      return true;
    };
    return ee.addListener(event, new_handler);
  };

  escapable = /[\x00-\x1f\ud800-\udfff\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufff0-\uffff]/g;

  unroll_lookup = function(escapable) {
    var c, i, unrolled;
    unrolled = {};
    c = (function() {
      var j, results;
      results = [];
      for (i = j = 0; j < 65536; i = ++j) {
        results.push(String.fromCharCode(i));
      }
      return results;
    })();
    escapable.lastIndex = 0;
    c.join('').replace(escapable, function(a) {
      return unrolled[a] = '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
    });
    return unrolled;
  };

  lookup = unroll_lookup(escapable);

  exports.quote = function(string) {
    var quoted;
    quoted = JSON.stringify(string);
    escapable.lastIndex = 0;
    if (!escapable.test(quoted)) {
      return quoted;
    }
    return quoted.replace(escapable, function(a) {
      return lookup[a];
    });
  };

  exports.parseCookie = function(cookie_header) {
    var cookie, cookies, j, len, parts, ref;
    cookies = {};
    if (cookie_header) {
      ref = cookie_header.split(';');
      for (j = 0, len = ref.length; j < len; j++) {
        cookie = ref[j];
        parts = cookie.split('=');
        cookies[parts[0].trim()] = (parts[1] || '').trim();
      }
    }
    return cookies;
  };

  exports.random32 = function() {
    var foo, v;
    foo = crypto.randomBytes(4);
    v = [foo[0], foo[1], foo[2], foo[3]];
    return v[0] + (v[1] * 256) + (v[2] * 256 * 256) + (v[3] * 256 * 256 * 256);
  };

}).call(this);
