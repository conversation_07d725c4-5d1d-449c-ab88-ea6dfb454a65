# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.8](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.7...v1.0.8) - 2024-03-21

### Commits

- [actions] use reusable workflows [`d139c11`](https://github.com/es-shims/String.prototype.trimStart/commit/d139c119ba5003936649741fae52b28080794dd3)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`16ff815`](https://github.com/es-shims/String.prototype.trimStart/commit/16ff81546c62ac3159fdf223cd1b035729fd55f0)
- [Deps] update `call-bind`, `define-properties` [`8dd308d`](https://github.com/es-shims/String.prototype.trimStart/commit/8dd308dd225ea3cb6a4d2c9662e435101367a5ef)
- [Refactor] use `es-object-atoms` instead of `es-abstract` [`4868f56`](https://github.com/es-shims/String.prototype.trimStart/commit/4868f566f8052ab7e28ce733ecc62b56f743f7fc)
- [meta] add missing `engines.node` [`8c1cce6`](https://github.com/es-shims/String.prototype.trimStart/commit/8c1cce612e4e1fdd1e5172c9e508a88abd3e6a5c)

## [v1.0.7](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.6...v1.0.7) - 2023-09-04

### Commits

- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`58e7aa6`](https://github.com/es-shims/String.prototype.trimStart/commit/58e7aa63b702f4494b89b8723b585bab5899a304)
- [Deps] update `define-properties`, `es-abstract` [`8d9a7bf`](https://github.com/es-shims/String.prototype.trimStart/commit/8d9a7bf123f320799211dd57aa0c832aaafb8a19)

## [v1.0.6](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.5...v1.0.6) - 2022-11-07

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`0838ae4`](https://github.com/es-shims/String.prototype.trimStart/commit/0838ae4ed948df470185afbeea4296f5c5ecd759)
- [actions] update rebase action to use reusable workflow [`d6bb784`](https://github.com/es-shims/String.prototype.trimStart/commit/d6bb78400a00459e9ab0b28f433c45804a65184d)
- [Dev Deps] update `aud`, `tape` [`8734d9a`](https://github.com/es-shims/String.prototype.trimStart/commit/8734d9a616c6aca9ec7f0c8819e66c2496deb32e)
- [Deps] update `es-abstract` [`30f593f`](https://github.com/es-shims/String.prototype.trimStart/commit/30f593f3febf6d8d9b3d5605174b437fed9cbb3a)

## [v1.0.5](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.4...v1.0.5) - 2022-05-02

### Commits

- [actions] reuse common workflows [`61d4009`](https://github.com/es-shims/String.prototype.trimStart/commit/61d40098a96b3dcc6cf963c9d03dd1b23b6eb13d)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`bfe39c4`](https://github.com/es-shims/String.prototype.trimStart/commit/bfe39c498e26a4ad75c64cce27ff3df9bfef2cc1)
- [Fix] ensure main entry point properly checks the receiver in ES3 engines [`36e3730`](https://github.com/es-shims/String.prototype.trimStart/commit/36e37307bb06fb5dc34d861a4bf6f7db931340d9)
- [Fix] as of unicode v6, the mongolian vowel separator is no longer whitespace [`4f77eed`](https://github.com/es-shims/String.prototype.trimStart/commit/4f77eed5aa64e6f781310847d7831563fce90f5c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`59fcb99`](https://github.com/es-shims/String.prototype.trimStart/commit/59fcb995ef8d4723a83e28d46dbe41a14253b681)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `functions-have-names`, `tape` [`486ffcf`](https://github.com/es-shims/String.prototype.trimStart/commit/486ffcfa93c75516601169a3973b81fdfeb7de8b)
- [actions] update codecov uploader [`b33ac48`](https://github.com/es-shims/String.prototype.trimStart/commit/b33ac48ccc86f6b894d8fde5162bd1c806904972)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `tape` [`3c89fa5`](https://github.com/es-shims/String.prototype.trimStart/commit/3c89fa571d1c4b8894230d93caaf3b3d9885534d)
- [readme] add github actions/codecov badges [`00be6b3`](https://github.com/es-shims/String.prototype.trimStart/commit/00be6b34fdd989950b934b4471087362b78d4b0c)
- [Dev Deps] update `eslint`, `tape` [`13a08f5`](https://github.com/es-shims/String.prototype.trimStart/commit/13a08f578c53cf5f129fb6ebe486e8bbcc40c14e)
- [actions] update workflows [`6ac576d`](https://github.com/es-shims/String.prototype.trimStart/commit/6ac576dde99f73d19492777efed29ec069e89bf1)
- [meta] use `prepublishOnly` script for npm 7+ [`fa382ca`](https://github.com/es-shims/String.prototype.trimStart/commit/fa382caa5c8e9a868572605ce215a3db1dc9ef21)
- [Deps] update `define-properties` [`d57bffe`](https://github.com/es-shims/String.prototype.trimStart/commit/d57bffef7d89384c8888627c5ee33b80e24d54e0)

## [v1.0.4](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.3...v1.0.4) - 2021-02-23

### Commits

- [meta] do not publish github action workflow files [`9c434ec`](https://github.com/es-shims/String.prototype.trimStart/commit/9c434eceb50141cf36e8e65f514226b0b547b568)
- [readme] remove travis badge [`7843160`](https://github.com/es-shims/String.prototype.trimStart/commit/7843160a3e8feaa2213feb0da9c5ad7d9bf21b59)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `functions-have-names`, `has-strict-mode`, `tape` [`8b52646`](https://github.com/es-shims/String.prototype.trimStart/commit/8b52646510aea20473da5491fe0876117a2251b1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`badeda2`](https://github.com/es-shims/String.prototype.trimStart/commit/badeda2b01d2e266e4d1c2e7dc0e4fca0f066f3e)
- [Tests] increase coverage [`bf8777d`](https://github.com/es-shims/String.prototype.trimStart/commit/bf8777d54c4b05c093559021cfaf0670306120f8)
- [actions] update workflows [`61be1c6`](https://github.com/es-shims/String.prototype.trimStart/commit/61be1c649ae859faa40286e57fa22cef65ae1229)
- [meta] gitignore coverage output [`c9c98d7`](https://github.com/es-shims/String.prototype.trimStart/commit/c9c98d75d7708e8906a39b55a0ad7a0ed6a9e4b0)
- [Deps] update `call-bind` [`c8645e8`](https://github.com/es-shims/String.prototype.trimStart/commit/c8645e89f9ace7681660ba66c724cf00c798f3d4)

## [v1.0.3](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.2...v1.0.3) - 2020-11-21

### Commits

- [Tests] migrate tests to Github Actions [`fbc7519`](https://github.com/es-shims/String.prototype.trimStart/commit/fbc7519cce2b5bfff9fe28dea96fb5f6f82e19fd)
- [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner [`3c9330b`](https://github.com/es-shims/String.prototype.trimStart/commit/3c9330be9ad02497f78ff0fd94b7c918c3a4bc21)
- [Tests] run `nyc` on all tests [`52229ca`](https://github.com/es-shims/String.prototype.trimStart/commit/52229ca28426be516c3826743e417be85144673e)
- [Deps] replace `es-abstract` with `call-bind` [`5e5068d`](https://github.com/es-shims/String.prototype.trimStart/commit/5e5068d2cc85d0a6f2a441ea984521ee70470537)
- [Dev Deps] update `eslint`, `aud`; add `safe-publish-latest` [`42a853e`](https://github.com/es-shims/String.prototype.trimStart/commit/42a853e2cb419378085098cb66e421ee94eed3ab)

## [v1.0.2](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.1...v1.0.2) - 2020-10-20

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`d032b38`](https://github.com/es-shims/String.prototype.trimStart/commit/d032b38aac7e9ebae7bf5c4195492c508af2815a)
- [actions] add "Allow Edits" workflow [`83e30ba`](https://github.com/es-shims/String.prototype.trimStart/commit/83e30bac01572b6dba6358fec6e339c55dc431c9)
- [Deps] update `es-abstract` [`707d85d`](https://github.com/es-shims/String.prototype.trimStart/commit/707d85d827d9c537a144f199fdecc47edaade1cd)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`096c6d9`](https://github.com/es-shims/String.prototype.trimStart/commit/096c6d9dc142286c750da7024e7a88ed698a4953)

## [v1.0.1](https://github.com/es-shims/String.prototype.trimStart/compare/v1.0.0...v1.0.1) - 2020-04-09

### Commits

- [meta] add some missing repo metadata [`3385da3`](https://github.com/es-shims/String.prototype.trimStart/commit/3385da3bbb87819de11a869981ca954887a6a092)
- [Dev Deps] update `auto-changelog` [`879377d`](https://github.com/es-shims/String.prototype.trimStart/commit/879377df9c1ff97d8f0b3eac800683f1d68a304c)

## [v1.0.0](https://github.com/es-shims/String.prototype.trimStart/compare/v0.1.0...v1.0.0) - 2020-03-30

### Commits

- [Breaking] convert to es-shim API [`970922c`](https://github.com/es-shims/String.prototype.trimStart/commit/970922c494c78b033c351c77f61a8aefd49c30d9)
- [meta] add `auto-changelog` [`ff30c09`](https://github.com/es-shims/String.prototype.trimStart/commit/ff30c0996289113d2c3dbbfca7e280ff151bf36d)
- [meta] update readme [`816291d`](https://github.com/es-shims/String.prototype.trimStart/commit/816291d01e0eaf85da9b732c179cfb2454bd282e)
- [Tests] add `npm run lint` [`3341104`](https://github.com/es-shims/String.prototype.trimStart/commit/3341104450bc6ac84f3b70a6d6c0fbeb4df5131e)
- Only apps should have lockfiles [`f008df7`](https://github.com/es-shims/String.prototype.trimStart/commit/f008df73fbf3dcf8dfad6d5cad86de7050d0ae09)
- [actions] add automatic rebasing / merge commit blocking [`e5ba35c`](https://github.com/es-shims/String.prototype.trimStart/commit/e5ba35c1a14fcf652336cc9c4be49d232981161e)
- [Tests] use shared travis-ci configs [`46516b1`](https://github.com/es-shims/String.prototype.trimStart/commit/46516b137a8c07ed5807d751bd61199688ef9baa)
- [meta] add `funding` field [`34ae856`](https://github.com/es-shims/String.prototype.trimStart/commit/34ae8563f115bd4a5e5f5d2d786c0fa0a420fa2a)
- [meta] fix non-updated version number [`3b0e262`](https://github.com/es-shims/String.prototype.trimStart/commit/3b0e262e2f4eeee2e1b99fe890f8ca17bed8f2fd)

## [v0.1.0](https://github.com/es-shims/String.prototype.trimStart/compare/v0.0.1...v0.1.0) - 2017-12-19

### Commits

- updated README [`ab2f6ac`](https://github.com/es-shims/String.prototype.trimStart/commit/ab2f6ac8813ed336a0f2dc3aa8cdb52f4d52814b)

## v0.0.1 - 2017-12-19

### Commits

- finished polyfill [`1c7ca20`](https://github.com/es-shims/String.prototype.trimStart/commit/1c7ca2043e3383b6e743870bc622ad4a38477147)
- created README file: [`192ecad`](https://github.com/es-shims/String.prototype.trimStart/commit/192ecaded4e0d5baaa65cd41e590b8d837520d44)
- Initial commit [`14044f8`](https://github.com/es-shims/String.prototype.trimStart/commit/14044f8a0fe1d155fe7403a8327bdbaf135da2d6)
- updated README [`d4fb6be`](https://github.com/es-shims/String.prototype.trimStart/commit/d4fb6be15455dd68fc4b306bee1d30dd4afc96e7)
