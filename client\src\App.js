import React, { useState, useEffect } from 'react';
import io from 'socket.io-client';
import './App.css';
import GameLobby from './components/GameLobby';
import GameRoom from './components/GameRoom';
import CreateGame from './components/CreateGame';
import JoinGame from './components/JoinGame';

const SERVER_URL = process.env.REACT_APP_SERVER_URL || 'http://localhost:5000';

function App() {
  const [socket, setSocket] = useState(null);
  const [gameState, setGameState] = useState('menu'); // menu, create, join, lobby, game
  const [gameData, setGameData] = useState(null);
  const [playerData, setPlayerData] = useState(null);
  const [error, setError] = useState('');

  useEffect(() => {
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    // Socket event listeners
    newSocket.on('game_created', (data) => {
      setPlayerData(data);
      setGameState('lobby');
    });

    newSocket.on('game_joined', (data) => {
      setPlayerData(data);
      setGameState('lobby');
    });

    newSocket.on('game_updated', (data) => {
      setGameData(data);
    });

    newSocket.on('game_started', (data) => {
      setGameState('game');
      setGameData(prev => ({ ...prev, ...data }));
    });

    newSocket.on('error', (data) => {
      setError(data.message);
      setTimeout(() => setError(''), 5000);
    });

    return () => newSocket.close();
  }, []);

  const handleCreateGame = (playerName, gameSettings) => {
    if (socket) {
      socket.emit('create_game', { playerName, gameSettings });
    }
  };

  const handleJoinGame = (gameId, playerName) => {
    if (socket) {
      socket.emit('join_game', { gameId, playerName });
    }
  };

  const handleStartGame = () => {
    if (socket) {
      socket.emit('start_game');
    }
  };

  const handleBackToMenu = () => {
    setGameState('menu');
    setGameData(null);
    setPlayerData(null);
    setError('');
  };

  return (
    <div className="App">
      {error && (
        <div className="error-banner">
          {error}
        </div>
      )}

      {gameState === 'menu' && (
        <div className="menu-container">
          <h1 className="game-title">Mafia Game</h1>
          <div className="menu-buttons">
            <button
              className="menu-btn create-btn"
              onClick={() => setGameState('create')}
            >
              Create Game
            </button>
            <button
              className="menu-btn join-btn"
              onClick={() => setGameState('join')}
            >
              Join Game
            </button>
          </div>
        </div>
      )}

      {gameState === 'create' && (
        <CreateGame
          onCreateGame={handleCreateGame}
          onBack={handleBackToMenu}
        />
      )}

      {gameState === 'join' && (
        <JoinGame
          onJoinGame={handleJoinGame}
          onBack={handleBackToMenu}
        />
      )}

      {gameState === 'lobby' && (
        <GameLobby
          socket={socket}
          gameData={gameData}
          playerData={playerData}
          onStartGame={handleStartGame}
          onBack={handleBackToMenu}
        />
      )}

      {gameState === 'game' && (
        <GameRoom
          socket={socket}
          gameData={gameData}
          playerData={playerData}
          onBack={handleBackToMenu}
        />
      )}
    </div>
  );
}

export default App;
