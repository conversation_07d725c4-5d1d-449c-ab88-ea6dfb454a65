{"name": "jest-watch-typeahead", "version": "1.1.0", "main": "build/index.js", "exports": {".": "./build/index.js", "./filename": "./build/file_name_plugin/plugin.js", "./testname": "./build/test_name_plugin/plugin.js", "./package.json": "./package.json"}, "type": "module", "author": "<PERSON><PERSON><PERSON> <rogeliogu<PERSON><EMAIL>>", "description": "Jest plugin for filtering by filename or test name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jest-community/jest-watch-typeahead.git"}, "homepage": "https://github.com/jest-community/jest-watch-typeahead", "files": ["build/", "filename.js", "testname.js"], "scripts": {"test": "cross-env NODE_OPTIONS=\"--experimental-vm-modules\" jest", "lint": "eslint .", "prebuild": "<PERSON><PERSON><PERSON> build", "build": "babel --extensions .js,.ts src -d build && rimraf **/*.test.{js,ts},integration build/**/__tests__ build/test_utils", "prepublish": "yarn build", "format": "prettier --write \"**/*.js\" \"**/*.md\" \"**/*.ts\"", "typecheck": "yarn tsc -p ."}, "dependencies": {"ansi-escapes": "^4.3.1", "chalk": "^4.0.0", "jest-regex-util": "^28.0.0", "jest-watcher": "^28.0.0", "slash": "^4.0.0", "string-length": "^5.0.1", "strip-ansi": "^7.0.1"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.10.4", "@jest/globals": "^28.0.0", "@jest/types": "^28.0.0", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.0", "@types/jest": "^27.0.0", "@types/node": "^16.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "babel-jest": "^28.0.0", "babel-plugin-add-import-extension": "^1.6.0", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^26.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.0.0", "prettier": "^2.1.1", "rimraf": "^3.0.2", "semantic-release": "^17.4.3", "semver": "^7.3.5", "typescript": "^4.0.2"}, "peerDependencies": {"jest": "^27.0.0 || ^28.0.0"}, "jest": {"extensionsToTreatAsEsm": [".ts"], "watchPlugins": ["<rootDir>/filename", "<rootDir>/testname"], "snapshotSerializers": ["<rootDir>/node_modules/pretty-format/build/plugins/ConvertAnsi"], "testPathIgnorePatterns": ["<rootDir>/build/.*", "<rootDir>/src/__tests__/pluginTester.js"], "transformIgnorePatterns": ["/node_modules/", "/__mocks__/"]}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}}